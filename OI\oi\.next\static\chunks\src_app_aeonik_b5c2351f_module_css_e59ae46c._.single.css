/* [project]/src/app/aeonik_b5c2351f.module.css [app-client] (css) */
@font-face {
  font-family: aeonik;
  src: url("../media/Aeonik_Medium-s.p.dcab127d.woff2") format("woff2");
  font-display: swap;
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: aeonik Fallback;
  src: local(Arial);
  ascent-override: 89.62%;
  descent-override: 20.24%;
  line-gap-override: 0.0%;
  size-adjust: 103.77%;
}

.aeonik_b5c2351f-module__8OGFfa__className {
  font-family: aeonik, aeonik Fallback;
}

.aeonik_b5c2351f-module__8OGFfa__variable {
  --font-aeonik: "aeonik", "aeonik Fallback";
}

/*# sourceMappingURL=src_app_aeonik_b5c2351f_module_css_e59ae46c._.single.css.map*/