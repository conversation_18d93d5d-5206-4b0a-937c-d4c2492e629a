'use client';

import { useState, useEffect } from 'react';
import Image from "next/image";

function Loading({ onLoadingComplete , loadingDuration = 4000 }) {
  const [progress, setProgress] = useState(0);
  const [displayProgress, setDisplayProgress] = useState(0);
  const [isComplete, setIsComplete] = useState(false);

  useEffect(() => {
    const startTime = Date.now();
    const interval = setInterval(() => {
      const elapsed = Date.now() - startTime;
      const newProgress = Math.min((elapsed / loadingDuration) * 100, 100);
      
      setProgress(newProgress);
      
      // Animate the display number with slight delay for smoother effect
      setTimeout(() => {
        setDisplayProgress(newProgress);
      }, 50);
      
      if (newProgress >= 100) {
        clearInterval(interval);
        setIsComplete(true);
        
        // Wait a bit before transitioning
        setTimeout(() => {
          if (onLoadingComplete) {
            onLoadingComplete();
          }
        }, 800);
      }
    }, 16); // ~60fps updates

    return () => clearInterval(interval);
  }, [loadingDuration, onLoadingComplete]);

  return (
    <div className={`fixed inset-0 bg-black flex flex-col justify-center items-center transition-all duration-1000 ease-in-out ${isComplete ? 'opacity-0 scale-105' : 'opacity-100 scale-100'}`}>
      {/* Progress Bar Container */}
      <div className="relative w-48 h-2 sm:w-64 sm:h-2 md:w-80 md:h-3 bg-gray-900 rounded-full overflow-hidden shadow-2xl">
        {/* Background glow */}
        <div className="absolute inset-0 bg-gradient-to-r from-gray-800 via-gray-700 to-gray-800 rounded-full"></div>
        
        {/* Progress fill with glow effect */}
        <div 
          className="absolute top-0 left-0 h-full bg-gradient-to-r from-white via-gray-100 to-white rounded-full transition-all duration-100 ease-out shadow-lg"
          style={{ 
            width: `${progress}%`,
            boxShadow: `0 0 20px rgba(255, 255, 255, ${progress / 100 * 0.8}), inset 0 1px 0 rgba(255, 255, 255, 0.5)`
          }}
        >
          {/* Moving shine effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent rounded-full animate-pulse"></div>
        </div>
        
        {/* Outer glow ring */}
        <div 
          className="absolute -inset-1 bg-white/10 rounded-full blur-sm transition-opacity duration-300"
          style={{ opacity: progress / 100 * 0.6 }}
        ></div>
      </div>
      
      {/* Counter with enhanced styling */}
      <div className="absolute bottom-6 left-6 sm:bottom-8 sm:left-8 md:bottom-12 md:left-12">
        <div className="relative">
          {/* Glow background */}
          <div className="absolute inset-0 text-white/20 text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold font-aeonik blur-sm transform translate-x-1 translate-y-1">
            {String(Math.floor(displayProgress)).padStart(3, '0')}
          </div>
          {/* Main text */}
          <span className="relative text-white text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold font-aeonik tracking-wider drop-shadow-2xl">
            {String(Math.floor(displayProgress)).padStart(3, '0')}
          </span>
        </div>
      </div>
      
      {/* Subtle particle effect */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-white/10 rounded-full animate-pulse"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${2 + Math.random() * 2}s`
            }}
          ></div>
        ))}
      </div>
    </div>
  );
}

export default function Home() {
  const [isLoading, setIsLoading] = useState(true);

  const handleLoadingComplete = () => {
    setIsLoading(false);
  };

  if (isLoading) {
    return <Loading onLoadingComplete={handleLoadingComplete} loadingDuration={4000} />;
  }

  return (
    <div className="font-sans grid grid-rows-[20px_1fr_20px] items-center justify-items-center min-h-screen p-8 pb-20 gap-16 sm:p-20 opacity-0 animate-fadeIn">
      <main className="flex flex-col gap-[32px] row-start-2 items-center sm:items-start">
        <Image
          className="dark:invert"
          src="/next.svg"
          alt="Next.js logo"
          width={180}
          height={38}
          priority
        />
        <ol className="font-mono list-inside list-decimal text-sm/6 text-center sm:text-left">
          <li className="mb-2 tracking-[-.01em]">
            Get started by editing{" "}
            <code className="bg-black/[.05] dark:bg-white/[.06] font-mono font-semibold px-1 py-0.5 rounded">
              src/app/page.tsx
            </code>
            .
          </li>
          <li className="tracking-[-.01em]">
            Save and see your changes instantly.
          </li>
        </ol>

        <div className="flex gap-4 items-center flex-col sm:flex-row">
          <a
            className="rounded-full border border-solid border-transparent transition-colors flex items-center justify-center bg-foreground text-background gap-2 hover:bg-[#383838] dark:hover:bg-[#ccc] font-medium text-sm sm:text-base h-10 sm:h-12 px-4 sm:px-5 sm:w-auto"
            href="https://vercel.com/new?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
            target="_blank"
            rel="noopener noreferrer"
          >
            <Image
              className="dark:invert"
              src="/vercel.svg"
              alt="Vercel logomark"
              width={20}
              height={20}
            />
            Deploy now
          </a>
          <a
            className="rounded-full border border-solid border-black/[.08] dark:border-white/[.145] transition-colors flex items-center justify-center hover:bg-[#f2f2f2] dark:hover:bg-[#1a1a1a] hover:border-transparent font-medium text-sm sm:text-base h-10 sm:h-12 px-4 sm:px-5 w-full sm:w-auto md:w-[158px]"
            href="https://nextjs.org/docs?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
            target="_blank"
            rel="noopener noreferrer"
          >
            Read our docs
          </a>
        </div>
      </main>
      <footer className="row-start-3 flex gap-[24px] flex-wrap items-center justify-center">
        <a
          className="flex items-center gap-2 hover:underline hover:underline-offset-4"
          href="https://nextjs.org/learn?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
          target="_blank"
          rel="noopener noreferrer"
        >
          <Image
            aria-hidden
            src="/file.svg"
            alt="File icon"
            width={16}
            height={16}
          />
          Learn
        </a>
        <a
          className="flex items-center gap-2 hover:underline hover:underline-offset-4"
          href="https://vercel.com/templates?framework=next.js&utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
          target="_blank"
          rel="noopener noreferrer"
        >
          <Image
            aria-hidden
            src="/window.svg"
            alt="Window icon"
            width={16}
            height={16}
          />
          Examples
        </a>
        <a
          className="flex items-center gap-2 hover:underline hover:underline-offset-4"
          href="https://nextjs.org?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
          target="_blank"
          rel="noopener noreferrer"
        >
          <Image
            aria-hidden
            src="/globe.svg"
            alt="Globe icon"
            width={16}
            height={16}
          />
          Go to nextjs.org →
        </a>
      </footer>
    </div>
  );
}