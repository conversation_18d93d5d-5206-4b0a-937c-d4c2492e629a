{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/Nextjs/Work/OI/oi/src/components/Loading.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useState } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\n\r\ninterface LoadingProps {\r\n  onLoadingComplete?: () => void;\r\n  loadingDuration?: number;\r\n}\r\n\r\nexport default function Loading({ onLoadingComplete, loadingDuration = 4000 }: LoadingProps) {\r\n  const [progress, setProgress] = useState(0);\r\n  const [isComplete, setIsComplete] = useState(false);\r\n  const router = useRouter();\r\n\r\n  useEffect(() => {\r\n    const startTime = Date.now();\r\n    const interval = setInterval(() => {\r\n      const elapsed = Date.now() - startTime;\r\n      const newProgress = Math.min((elapsed / loadingDuration) * 100, 100);\r\n      \r\n      setProgress(newProgress);\r\n      \r\n      if (newProgress >= 100) {\r\n        clearInterval(interval);\r\n        setIsComplete(true);\r\n        setTimeout(() => {\r\n          onLoadingComplete?.();\r\n        }, 800);\r\n      }\r\n    }, 30);\r\n\r\n    return () => clearInterval(interval);\r\n  }, [loadingDuration, onLoadingComplete]);\r\n\r\n  // Generate numbers for scrolling effect (0-100)\r\n  const numbers = Array.from({ length: 101 }, (_, i) => i);\r\n\r\n  return (\r\n    <div className={`fixed inset-0 bg-white flex flex-col justify-center items-center transition-all duration-1000 ease-in-out ${isComplete ? 'opacity-0 scale-0' : 'opacity-100 scale-100'}`}>\r\n      {/* O-shape container */}\r\n      <div className=\"relative w-64 h-64 rounded-full border-4 border-gray-200 overflow-hidden\">\r\n        {/* Water fill effect */}\r\n        <div \r\n          className=\"absolute bottom-0 left-0 right-0 bg-gray-800 transition-all duration-300 ease-out\"\r\n          style={{ height: `${progress}%` }}\r\n        >\r\n          {/* Water surface effect */}\r\n          <div className=\"absolute top-0 left-0 right-0 h-4 bg-white/30 animate-wave\"></div>\r\n        </div>\r\n        \r\n        {/* O-shape outline */}\r\n        <div className=\"absolute inset-0 rounded-full border-4 border-gray-800 pointer-events-none\"></div>\r\n      </div>\r\n      \r\n      {/* Scrolling numbers counter */}\r\n      <div className=\"relative h-16 mt-8 overflow-hidden\">\r\n        <div \r\n          className=\"flex flex-col items-center transition-transform duration-300 ease-out\"\r\n          style={{ transform: `translateY(-${progress * 0.64}px)` }}\r\n        >\r\n          {numbers.map((num) => (\r\n            <span \r\n              key={num} \r\n              className=\"text-5xl font-bold text-gray-800\"\r\n            >\r\n              {num}%\r\n            </span>\r\n          ))}\r\n        </div>\r\n      </div>\r\n      \r\n      {/* Subtle background elements */}\r\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\r\n        {[...Array(10)].map((_, i) => (\r\n          <div\r\n            key={i}\r\n            className=\"absolute w-1 h-1 bg-gray-200 rounded-full\"\r\n            style={{\r\n              left: `${Math.random() * 100}%`,\r\n              top: `${Math.random() * 100}%`,\r\n            }}\r\n          ></div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAUe,SAAS,QAAQ,EAAE,iBAAiB,EAAE,kBAAkB,IAAI,EAAgB;IACzF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,KAAK,GAAG;QAC1B,MAAM,WAAW,YAAY;YAC3B,MAAM,UAAU,KAAK,GAAG,KAAK;YAC7B,MAAM,cAAc,KAAK,GAAG,CAAC,AAAC,UAAU,kBAAmB,KAAK;YAEhE,YAAY;YAEZ,IAAI,eAAe,KAAK;gBACtB,cAAc;gBACd,cAAc;gBACd,WAAW;oBACT;gBACF,GAAG;YACL;QACF,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAiB;KAAkB;IAEvC,gDAAgD;IAChD,MAAM,UAAU,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAI,GAAG,CAAC,GAAG,IAAM;IAEtD,qBACE,8OAAC;QAAI,WAAW,CAAC,0GAA0G,EAAE,aAAa,sBAAsB,yBAAyB;;0BAEvL,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,QAAQ,GAAG,SAAS,CAAC,CAAC;wBAAC;kCAGhC,cAAA,8OAAC;4BAAI,WAAU;;;;;;;;;;;kCAIjB,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,WAAW,CAAC,YAAY,EAAE,WAAW,KAAK,GAAG,CAAC;oBAAC;8BAEvD,QAAQ,GAAG,CAAC,CAAC,oBACZ,8OAAC;4BAEC,WAAU;;gCAET;gCAAI;;2BAHA;;;;;;;;;;;;;;;0BAUb,8OAAC;gBAAI,WAAU;0BACZ;uBAAI,MAAM;iBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,8OAAC;wBAEC,WAAU;wBACV,OAAO;4BACL,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;4BAC/B,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;wBAChC;uBALK;;;;;;;;;;;;;;;;AAWjB", "debugId": null}}, {"offset": {"line": 169, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/Nextjs/Work/OI/oi/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Image from \"next/image\";\nimport Loading from \"@/components/Loading\";\n\nexport default function Home() {\n  const [isLoading, setIsLoading] = useState(true);\n\n  if (isLoading) {\n    return <Loading onLoadingComplete={() => setIsLoading(false)} />;\n  }\n\n  return (\n    <div className=\"font-sans grid grid-rows-[20px_1fr_20px] items-center justify-items-center min-h-screen p-8 pb-20 gap-16 sm:p-20 animate-fadeIn\">\n      <main className=\"flex flex-col gap-[32px] row-start-2 items-center sm:items-start\">\n        <Image\n          className=\"dark:invert\"\n          src=\"/next.svg\"\n          alt=\"Next.js logo\"\n          width={180}\n          height={38}\n          priority\n        />\n        <ol className=\"font-mono list-inside list-decimal text-sm/6 text-center sm:text-left\">\n          <li className=\"mb-2 tracking-[-.01em]\">\n            Get started by editing{\" \"}\n            <code className=\"bg-black/[.05] dark:bg-white/[.06] font-mono font-semibold px-1 py-0.5 rounded\">\n              src/app/page.tsx\n            </code>\n          </li>\n          <li className=\"tracking-[-.01em]\">\n            Save and see your changes instantly.\n          </li>\n        </ol>\n\n        <div className=\"flex gap-4 items-center flex-col sm:flex-row\">\n          <a\n            className=\"rounded-full border border-solid border-transparent transition-colors flex items-center justify-center bg-foreground text-background gap-2 hover:bg-[#383838] dark:hover:bg-[#ccc] font-medium text-sm sm:text-base h-10 sm:h-12 px-4 sm:px-5 sm:w-auto\"\n            href=\"https://vercel.com/new?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app\"\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n          >\n            <Image\n              className=\"dark:invert\"\n              src=\"/vercel.svg\"\n              alt=\"Vercel logomark\"\n              width={20}\n              height={20}\n            />\n            Deploy now\n          </a>\n          <a\n            className=\"rounded-full border border-solid border-black/[.08] dark:border-white/[.145] transition-colors flex items-center justify-center hover:bg-[#f2f2f2] dark:hover:bg-[#1a1a1a] hover:border-transparent font-medium text-sm sm:text-base h-10 sm:h-12 px-4 sm:px-5 w-full sm:w-auto md:w-[158px]\"\n            href=\"https://nextjs.org/docs?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app\"\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n          >\n            Read our docs\n          </a>\n        </div>\n      </main>\n      <footer className=\"row-start-3 flex gap-[24px] flex-wrap items-center justify-center\">\n        <a\n          className=\"flex items-center gap-2 hover:underline hover:underline-offset-4\"\n          href=\"https://nextjs.org/learn?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app\"\n          target=\"_blank\"\n          rel=\"noopener noreferrer\"\n        >\n          <Image\n            aria-hidden\n            src=\"/file.svg\"\n            alt=\"File icon\"\n            width={16}\n            height={16}\n          />\n          Learn\n        </a>\n        <a\n          className=\"flex items-center gap-2 hover:underline hover:underline-offset-4\"\n          href=\"https://vercel.com/templates?framework=next.js&utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app\"\n          target=\"_blank\"\n          rel=\"noopener noreferrer\"\n        >\n          <Image\n            aria-hidden\n            src=\"/window.svg\"\n            alt=\"Window icon\"\n            width={16}\n            height={16}\n          />\n          Examples\n        </a>\n        <a\n          className=\"flex items-center gap-2 hover:underline hover:underline-offset-4\"\n          href=\"https://nextjs.org?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app\"\n          target=\"_blank\"\n          rel=\"noopener noreferrer\"\n        >\n          <Image\n            aria-hidden\n            src=\"/globe.svg\"\n            alt=\"Globe icon\"\n            width={16}\n            height={16}\n          />\n          Go to nextjs.org →\n        </a>\n      </footer>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,IAAI,WAAW;QACb,qBAAO,8OAAC,6HAAA,CAAA,UAAO;YAAC,mBAAmB,IAAM,aAAa;;;;;;IACxD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAK,WAAU;;kCACd,8OAAC,6HAAA,CAAA,UAAK;wBACJ,WAAU;wBACV,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,QAAQ;;;;;;kCAEV,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;gCAAG,WAAU;;oCAAyB;oCACd;kDACvB,8OAAC;wCAAK,WAAU;kDAAiF;;;;;;;;;;;;0CAInG,8OAAC;gCAAG,WAAU;0CAAoB;;;;;;;;;;;;kCAKpC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,WAAU;gCACV,MAAK;gCACL,QAAO;gCACP,KAAI;;kDAEJ,8OAAC,6HAAA,CAAA,UAAK;wCACJ,WAAU;wCACV,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;;;;;;oCACR;;;;;;;0CAGJ,8OAAC;gCACC,WAAU;gCACV,MAAK;gCACL,QAAO;gCACP,KAAI;0CACL;;;;;;;;;;;;;;;;;;0BAKL,8OAAC;gBAAO,WAAU;;kCAChB,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,QAAO;wBACP,KAAI;;0CAEJ,8OAAC,6HAAA,CAAA,UAAK;gCACJ,aAAW;gCACX,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;;;;;;4BACR;;;;;;;kCAGJ,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,QAAO;wBACP,KAAI;;0CAEJ,8OAAC,6HAAA,CAAA,UAAK;gCACJ,aAAW;gCACX,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;;;;;;4BACR;;;;;;;kCAGJ,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,QAAO;wBACP,KAAI;;0CAEJ,8OAAC,6HAAA,CAAA,UAAK;gCACJ,aAAW;gCACX,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;;;;;;4BACR;;;;;;;;;;;;;;;;;;;AAMZ", "debugId": null}}]}