{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/Nextjs/Work/OI/oi/src/components/Loading.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useRef } from 'react';\r\n\r\ninterface LoadingProps {\r\n  onLoadingComplete?: () => void;\r\n  loadingDuration?: number;\r\n}\r\n\r\nexport default function Loading({ onLoadingComplete, loadingDuration = 4000 }: LoadingProps) {\r\n  const [progress, setProgress] = useState(0);\r\n  const [displayNumber, setDisplayNumber] = useState(0);\r\n  const [isComplete, setIsComplete] = useState(false);\r\n  const [isFlipping, setIsFlipping] = useState(false);\r\n  const prevNumberRef = useRef(0);\r\n\r\n  useEffect(() => {\r\n    const startTime = Date.now();\r\n    const interval = setInterval(() => {\r\n      const elapsed = Date.now() - startTime;\r\n      const newProgress = Math.min((elapsed / loadingDuration) * 100, 100);\r\n      const newNumber = Math.floor(newProgress);\r\n      \r\n      setProgress(newProgress);\r\n      \r\n      // Only trigger flip when whole number changes\r\n      if (newNumber !== Math.floor(progress)) {\r\n        prevNumberRef.current = displayNumber;\r\n        setIsFlipping(true);\r\n        setTimeout(() => {\r\n          setDisplayNumber(newNumber);\r\n          setIsFlipping(false);\r\n        }, 300); // Match this with flip animation duration\r\n      }\r\n      \r\n      if (newProgress >= 100) {\r\n        clearInterval(interval);\r\n        setIsComplete(true);\r\n        setTimeout(() => {\r\n          onLoadingComplete?.();\r\n        }, 800);\r\n      }\r\n    }, 30);\r\n\r\n    return () => clearInterval(interval);\r\n  }, [loadingDuration, onLoadingComplete, progress, displayNumber]);\r\n\r\n  return (\r\n    <div className={`fixed inset-0 bg-black flex flex-col justify-center items-center transition-all duration-1000 ease-in-out ${isComplete ? 'opacity-0 scale-105' : 'opacity-100 scale-100'}`}>\r\n      {/* Progress Bar Container */}\r\n      <div className=\"relative w-48 h-4 sm:w-64 sm:h-4 md:w-80 md:h-5 bg-gray-900 overflow-hidden shadow-2xl\">\r\n        <div className=\"absolute inset-0 bg-gradient-to-r from-gray-800 via-gray-700 to-gray-800\"></div>\r\n        \r\n        <div \r\n          className=\"absolute top-0 left-0 h-full bg-gradient-to-r from-white via-gray-100 to-white transition-all duration-100 ease-out shadow-lg\"\r\n          style={{ \r\n            width: `${progress}%`,\r\n            boxShadow: `0 0 20px rgba(255, 255, 255, ${progress / 100 * 0.8}), inset 0 1px 0 rgba(255, 255, 255, 0.5)`\r\n          }}\r\n        >\r\n          <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent rounded-full animate-pulse\"></div>\r\n        </div>\r\n        \r\n        <div \r\n          className=\"absolute -inset-1 bg-white/10 rounded-full blur-sm transition-opacity duration-300\"\r\n          style={{ opacity: progress / 100 * 0.6 }}\r\n        ></div>\r\n      </div>\r\n      \r\n      {/* Flip Clock Counter */}\r\n      <div className=\"absolute bottom-6 left-6 sm:bottom-8 sm:left-8 md:bottom-12 md:left-12\">\r\n        <div className=\"relative h-16 w-24 perspective-1000\">\r\n          {/* Current number */}\r\n          <div className=\"relative h-full w-full\">\r\n            {/* Top half (flipping out) */}\r\n            <div \r\n              className={`absolute top-0 left-0 right-0 h-1/2 bg-gray-800 flex items-end justify-center overflow-hidden origin-bottom transition-transform duration-300 ease-out ${isFlipping ? 'rotate-x-90' : ''}`}\r\n              style={{\r\n                borderRadius: '8px 8px 0 0',\r\n                backfaceVisibility: 'hidden',\r\n                zIndex: 2,\r\n                transform: isFlipping ? 'rotateX(90deg)' : 'rotateX(0deg)'\r\n              }}\r\n            >\r\n              <span className=\"text-white text-4xl sm:text-5xl md:text-6xl font-bold tracking-wider drop-shadow-2xl mb-[-0.25em]\">\r\n                {String(prevNumberRef.current).padStart(3, '0')}\r\n              </span>\r\n            </div>\r\n            \r\n            {/* Bottom half (static) */}\r\n            <div \r\n              className=\"absolute bottom-0 left-0 right-0 h-1/2 bg-gray-800 flex items-start justify-center overflow-hidden\"\r\n              style={{\r\n                borderRadius: '0 0 8px 8px',\r\n                zIndex: 1\r\n              }}\r\n            >\r\n              <span className=\"text-white text-4xl sm:text-5xl md:text-6xl font-bold tracking-wider drop-shadow-2xl mt-[-0.25em]\">\r\n                {String(prevNumberRef.current).padStart(3, '0')}\r\n              </span>\r\n            </div>\r\n            \r\n            {/* New top half (flipping in) */}\r\n            <div \r\n              className={`absolute top-0 left-0 right-0 h-1/2 bg-gray-800 flex items-end justify-center overflow-hidden origin-bottom transition-transform duration-300 ease-out ${isFlipping ? 'rotate-x-0' : 'rotate-x-90'}`}\r\n              style={{\r\n                borderRadius: '8px 8px 0 0',\r\n                backfaceVisibility: 'hidden',\r\n                zIndex: isFlipping ? 3 : 0,\r\n                transform: isFlipping ? 'rotateX(0deg)' : 'rotateX(90deg)'\r\n              }}\r\n            >\r\n              <span className=\"text-white text-4xl sm:text-5xl md:text-6xl font-bold tracking-wider drop-shadow-2xl mb-[-0.25em]\">\r\n                {String(displayNumber).padStart(3, '0')}\r\n              </span>\r\n            </div>\r\n          </div>\r\n          \r\n          {/* Glow background */}\r\n          <div className=\"absolute inset-0 text-white/20 text-4xl sm:text-5xl md:text-6xl font-bold blur-sm transform translate-x-1 translate-y-1\">\r\n            {String(displayNumber).padStart(3, '0')}\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      {/* Subtle particle effect */}\r\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\r\n        {[...Array(20)].map((_, i) => (\r\n          <div\r\n            key={i}\r\n            className=\"absolute w-1 h-1 bg-white/10 rounded-full animate-pulse\"\r\n            style={{\r\n              left: `${Math.random() * 100}%`,\r\n              top: `${Math.random() * 100}%`,\r\n              animationDelay: `${Math.random() * 3}s`,\r\n              animationDuration: `${2 + Math.random() * 2}s`\r\n            }}\r\n          ></div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASe,SAAS,QAAQ,EAAE,iBAAiB,EAAE,kBAAkB,IAAI,EAAgB;IACzF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,KAAK,GAAG;QAC1B,MAAM,WAAW,YAAY;YAC3B,MAAM,UAAU,KAAK,GAAG,KAAK;YAC7B,MAAM,cAAc,KAAK,GAAG,CAAC,AAAC,UAAU,kBAAmB,KAAK;YAChE,MAAM,YAAY,KAAK,KAAK,CAAC;YAE7B,YAAY;YAEZ,8CAA8C;YAC9C,IAAI,cAAc,KAAK,KAAK,CAAC,WAAW;gBACtC,cAAc,OAAO,GAAG;gBACxB,cAAc;gBACd,WAAW;oBACT,iBAAiB;oBACjB,cAAc;gBAChB,GAAG,MAAM,0CAA0C;YACrD;YAEA,IAAI,eAAe,KAAK;gBACtB,cAAc;gBACd,cAAc;gBACd,WAAW;oBACT;gBACF,GAAG;YACL;QACF,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAiB;QAAmB;QAAU;KAAc;IAEhE,qBACE,8OAAC;QAAI,WAAW,CAAC,0GAA0G,EAAE,aAAa,wBAAwB,yBAAyB;;0BAEzL,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCAEf,8OAAC;wBACC,WAAU;wBACV,OAAO;4BACL,OAAO,GAAG,SAAS,CAAC,CAAC;4BACrB,WAAW,CAAC,6BAA6B,EAAE,WAAW,MAAM,IAAI,yCAAyC,CAAC;wBAC5G;kCAEA,cAAA,8OAAC;4BAAI,WAAU;;;;;;;;;;;kCAGjB,8OAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,SAAS,WAAW,MAAM;wBAAI;;;;;;;;;;;;0BAK3C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCACC,WAAW,CAAC,uJAAuJ,EAAE,aAAa,gBAAgB,IAAI;oCACtM,OAAO;wCACL,cAAc;wCACd,oBAAoB;wCACpB,QAAQ;wCACR,WAAW,aAAa,mBAAmB;oCAC7C;8CAEA,cAAA,8OAAC;wCAAK,WAAU;kDACb,OAAO,cAAc,OAAO,EAAE,QAAQ,CAAC,GAAG;;;;;;;;;;;8CAK/C,8OAAC;oCACC,WAAU;oCACV,OAAO;wCACL,cAAc;wCACd,QAAQ;oCACV;8CAEA,cAAA,8OAAC;wCAAK,WAAU;kDACb,OAAO,cAAc,OAAO,EAAE,QAAQ,CAAC,GAAG;;;;;;;;;;;8CAK/C,8OAAC;oCACC,WAAW,CAAC,uJAAuJ,EAAE,aAAa,eAAe,eAAe;oCAChN,OAAO;wCACL,cAAc;wCACd,oBAAoB;wCACpB,QAAQ,aAAa,IAAI;wCACzB,WAAW,aAAa,kBAAkB;oCAC5C;8CAEA,cAAA,8OAAC;wCAAK,WAAU;kDACb,OAAO,eAAe,QAAQ,CAAC,GAAG;;;;;;;;;;;;;;;;;sCAMzC,8OAAC;4BAAI,WAAU;sCACZ,OAAO,eAAe,QAAQ,CAAC,GAAG;;;;;;;;;;;;;;;;;0BAMzC,8OAAC;gBAAI,WAAU;0BACZ;uBAAI,MAAM;iBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,8OAAC;wBAEC,WAAU;wBACV,OAAO;4BACL,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;4BAC/B,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;4BAC9B,gBAAgB,GAAG,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;4BACvC,mBAAmB,GAAG,IAAI,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;wBAChD;uBAPK;;;;;;;;;;;;;;;;AAajB", "debugId": null}}, {"offset": {"line": 232, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/Nextjs/Work/OI/oi/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Image from \"next/image\";\nimport Loading from \"@/components/Loading\";\n\nexport default function Home() {\n  const [isLoading, setIsLoading] = useState(true);\n\n  if (isLoading) {\n    return <Loading onLoadingComplete={() => setIsLoading(false)} />;\n  }\n\n  return (\n    <div className=\"font-sans grid grid-rows-[20px_1fr_20px] items-center justify-items-center min-h-screen p-8 pb-20 gap-16 sm:p-20 animate-fadeIn\">\n      <main className=\"flex flex-col gap-[32px] row-start-2 items-center sm:items-start\">\n        <Image\n          className=\"dark:invert\"\n          src=\"/next.svg\"\n          alt=\"Next.js logo\"\n          width={180}\n          height={38}\n          priority\n        />\n        <ol className=\"font-mono list-inside list-decimal text-sm/6 text-center sm:text-left\">\n          <li className=\"mb-2 tracking-[-.01em]\">\n            Get started by editing{\" \"}\n            <code className=\"bg-black/[.05] dark:bg-white/[.06] font-mono font-semibold px-1 py-0.5 rounded\">\n              src/app/page.tsx\n            </code>\n          </li>\n          <li className=\"tracking-[-.01em]\">\n            Save and see your changes instantly.\n          </li>\n        </ol>\n\n        <div className=\"flex gap-4 items-center flex-col sm:flex-row\">\n          <a\n            className=\"rounded-full border border-solid border-transparent transition-colors flex items-center justify-center bg-foreground text-background gap-2 hover:bg-[#383838] dark:hover:bg-[#ccc] font-medium text-sm sm:text-base h-10 sm:h-12 px-4 sm:px-5 sm:w-auto\"\n            href=\"https://vercel.com/new?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app\"\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n          >\n            <Image\n              className=\"dark:invert\"\n              src=\"/vercel.svg\"\n              alt=\"Vercel logomark\"\n              width={20}\n              height={20}\n            />\n            Deploy now\n          </a>\n          <a\n            className=\"rounded-full border border-solid border-black/[.08] dark:border-white/[.145] transition-colors flex items-center justify-center hover:bg-[#f2f2f2] dark:hover:bg-[#1a1a1a] hover:border-transparent font-medium text-sm sm:text-base h-10 sm:h-12 px-4 sm:px-5 w-full sm:w-auto md:w-[158px]\"\n            href=\"https://nextjs.org/docs?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app\"\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n          >\n            Read our docs\n          </a>\n        </div>\n      </main>\n      <footer className=\"row-start-3 flex gap-[24px] flex-wrap items-center justify-center\">\n        <a\n          className=\"flex items-center gap-2 hover:underline hover:underline-offset-4\"\n          href=\"https://nextjs.org/learn?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app\"\n          target=\"_blank\"\n          rel=\"noopener noreferrer\"\n        >\n          <Image\n            aria-hidden\n            src=\"/file.svg\"\n            alt=\"File icon\"\n            width={16}\n            height={16}\n          />\n          Learn\n        </a>\n        <a\n          className=\"flex items-center gap-2 hover:underline hover:underline-offset-4\"\n          href=\"https://vercel.com/templates?framework=next.js&utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app\"\n          target=\"_blank\"\n          rel=\"noopener noreferrer\"\n        >\n          <Image\n            aria-hidden\n            src=\"/window.svg\"\n            alt=\"Window icon\"\n            width={16}\n            height={16}\n          />\n          Examples\n        </a>\n        <a\n          className=\"flex items-center gap-2 hover:underline hover:underline-offset-4\"\n          href=\"https://nextjs.org?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app\"\n          target=\"_blank\"\n          rel=\"noopener noreferrer\"\n        >\n          <Image\n            aria-hidden\n            src=\"/globe.svg\"\n            alt=\"Globe icon\"\n            width={16}\n            height={16}\n          />\n          Go to nextjs.org →\n        </a>\n      </footer>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,IAAI,WAAW;QACb,qBAAO,8OAAC,6HAAA,CAAA,UAAO;YAAC,mBAAmB,IAAM,aAAa;;;;;;IACxD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAK,WAAU;;kCACd,8OAAC,6HAAA,CAAA,UAAK;wBACJ,WAAU;wBACV,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,QAAQ;;;;;;kCAEV,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;gCAAG,WAAU;;oCAAyB;oCACd;kDACvB,8OAAC;wCAAK,WAAU;kDAAiF;;;;;;;;;;;;0CAInG,8OAAC;gCAAG,WAAU;0CAAoB;;;;;;;;;;;;kCAKpC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,WAAU;gCACV,MAAK;gCACL,QAAO;gCACP,KAAI;;kDAEJ,8OAAC,6HAAA,CAAA,UAAK;wCACJ,WAAU;wCACV,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;;;;;;oCACR;;;;;;;0CAGJ,8OAAC;gCACC,WAAU;gCACV,MAAK;gCACL,QAAO;gCACP,KAAI;0CACL;;;;;;;;;;;;;;;;;;0BAKL,8OAAC;gBAAO,WAAU;;kCAChB,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,QAAO;wBACP,KAAI;;0CAEJ,8OAAC,6HAAA,CAAA,UAAK;gCACJ,aAAW;gCACX,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;;;;;;4BACR;;;;;;;kCAGJ,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,QAAO;wBACP,KAAI;;0CAEJ,8OAAC,6HAAA,CAAA,UAAK;gCACJ,aAAW;gCACX,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;;;;;;4BACR;;;;;;;kCAGJ,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,QAAO;wBACP,KAAI;;0CAEJ,8OAAC,6HAAA,CAAA,UAAK;gCACJ,aAAW;gCACX,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;;;;;;4BACR;;;;;;;;;;;;;;;;;;;AAMZ", "debugId": null}}]}