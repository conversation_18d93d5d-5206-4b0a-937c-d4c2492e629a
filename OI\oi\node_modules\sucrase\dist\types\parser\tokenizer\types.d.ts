/**
 * Enum of all token types, with bit fields to signify meaningful properties.
 */
export declare enum TokenType {
    PRECEDENCE_MASK = 15,
    IS_KEYWORD = 16,
    IS_ASSIGN = 32,
    IS_RIGHT_ASSOCIATIVE = 64,
    IS_PREFIX = 128,
    IS_POSTFIX = 256,
    IS_EXPRESSION_START = 512,
    num = 512,
    bigint = 1536,
    decimal = 2560,
    regexp = 3584,
    string = 4608,
    name = 5632,
    eof = 6144,
    bracketL = 7680,
    bracketR = 8192,
    braceL = 9728,
    braceBarL = 10752,
    braceR = 11264,
    braceBarR = 12288,
    parenL = 13824,
    parenR = 14336,
    comma = 15360,
    semi = 16384,
    colon = 17408,
    doubleColon = 18432,
    dot = 19456,
    question = 20480,
    questionDot = 21504,
    arrow = 22528,
    template = 23552,
    ellipsis = 24576,
    backQuote = 25600,
    dollarBraceL = 27136,
    at = 27648,
    hash = 29184,
    eq = 29728,
    assign = 30752,
    preIncDec = 32640,
    postIncDec = 33664,
    bang = 34432,
    tilde = 35456,
    pipeline = 35841,
    nullishCoalescing = 36866,
    logicalOR = 37890,
    logicalAND = 38915,
    bitwiseOR = 39940,
    bitwiseXOR = 40965,
    bitwiseAND = 41990,
    equality = 43015,
    lessThan = 44040,
    greaterThan = 45064,
    relationalOrEqual = 46088,
    bitShiftL = 47113,
    bitShiftR = 48137,
    plus = 49802,
    minus = 50826,
    modulo = 51723,
    star = 52235,
    slash = 53259,
    exponent = 54348,
    jsxName = 55296,
    jsxText = 56320,
    jsxEmptyText = 57344,
    jsxTagStart = 58880,
    jsxTagEnd = 59392,
    typeParameterStart = 60928,
    nonNullAssertion = 61440,
    _break = 62480,
    _case = 63504,
    _catch = 64528,
    _continue = 65552,
    _debugger = 66576,
    _default = 67600,
    _do = 68624,
    _else = 69648,
    _finally = 70672,
    _for = 71696,
    _function = 73232,
    _if = 73744,
    _return = 74768,
    _switch = 75792,
    _throw = 77456,
    _try = 77840,
    _var = 78864,
    _let = 79888,
    _const = 80912,
    _while = 81936,
    _with = 82960,
    _new = 84496,
    _this = 85520,
    _super = 86544,
    _class = 87568,
    _extends = 88080,
    _export = 89104,
    _import = 90640,
    _yield = 91664,
    _null = 92688,
    _true = 93712,
    _false = 94736,
    _in = 95256,
    _instanceof = 96280,
    _typeof = 97936,
    _void = 98960,
    _delete = 99984,
    _async = 100880,
    _get = 101904,
    _set = 102928,
    _declare = 103952,
    _readonly = 104976,
    _abstract = 106000,
    _static = 107024,
    _public = 107536,
    _private = 108560,
    _protected = 109584,
    _override = 110608,
    _as = 112144,
    _enum = 113168,
    _type = 114192,
    _implements = 115216
}
export declare function formatTokenType(tokenType: TokenType): string;
