{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/Nextjs/Work/OI/oi/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Image from \"next/image\";\n\nfunction Loading({ onLoadingComplete, loadingDuration = 4000 }) {\n  const [progress, setProgress] = useState(0);\n  const [displayProgress, setDisplayProgress] = useState(0);\n  const [isComplete, setIsComplete] = useState(false);\n\n  useEffect(() => {\n    const startTime = Date.now();\n    const interval = setInterval(() => {\n      const elapsed = Date.now() - startTime;\n      const newProgress = Math.min((elapsed / loadingDuration) * 100, 100);\n      \n      setProgress(newProgress);\n      \n      // Animate the display number with slight delay for smoother effect\n      setTimeout(() => {\n        setDisplayProgress(newProgress);\n      }, 50);\n      \n      if (newProgress >= 100) {\n        clearInterval(interval);\n        setIsComplete(true);\n        \n        // Wait a bit before transitioning\n        setTimeout(() => {\n          if (onLoadingComplete) {\n            onLoadingComplete();\n          }\n        }, 800);\n      }\n    }, 16); // ~60fps updates\n\n    return () => clearInterval(interval);\n  }, [loadingDuration, onLoadingComplete]);\n\n  return (\n    <div className={`fixed inset-0 bg-black flex flex-col justify-center items-center transition-all duration-1000 ease-in-out ${isComplete ? 'opacity-0 scale-105' : 'opacity-100 scale-100'}`}>\n      {/* Progress Bar Container */}\n      <div className=\"relative w-48 h-2 sm:w-64 sm:h-2 md:w-80 md:h-3 bg-gray-900 rounded-full overflow-hidden shadow-2xl\">\n        {/* Background glow */}\n        <div className=\"absolute inset-0 bg-gradient-to-r from-gray-800 via-gray-700 to-gray-800 rounded-full\"></div>\n        \n        {/* Progress fill with glow effect */}\n        <div \n          className=\"absolute top-0 left-0 h-full bg-gradient-to-r from-white via-gray-100 to-white rounded-full transition-all duration-100 ease-out shadow-lg\"\n          style={{ \n            width: `${progress}%`,\n            boxShadow: `0 0 20px rgba(255, 255, 255, ${progress / 100 * 0.8}), inset 0 1px 0 rgba(255, 255, 255, 0.5)`\n          }}\n        >\n          {/* Moving shine effect */}\n          <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent rounded-full animate-pulse\"></div>\n        </div>\n        \n        {/* Outer glow ring */}\n        <div \n          className=\"absolute -inset-1 bg-white/10 rounded-full blur-sm transition-opacity duration-300\"\n          style={{ opacity: progress / 100 * 0.6 }}\n        ></div>\n      </div>\n      \n      {/* Counter with enhanced styling */}\n      <div className=\"absolute bottom-6 left-6 sm:bottom-8 sm:left-8 md:bottom-12 md:left-12\">\n        <div className=\"relative\">\n          {/* Glow background */}\n          <div className=\"absolute inset-0 text-white/20 text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold font-aeonik blur-sm transform translate-x-1 translate-y-1\">\n            {String(Math.floor(displayProgress)).padStart(3, '0')}\n          </div>\n          {/* Main text */}\n          <span className=\"relative text-white text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold font-aeonik tracking-wider drop-shadow-2xl\">\n            {String(Math.floor(displayProgress)).padStart(3, '0')}\n          </span>\n        </div>\n      </div>\n      \n      {/* Subtle particle effect */}\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        {[...Array(20)].map((_, i) => (\n          <div\n            key={i}\n            className=\"absolute w-1 h-1 bg-white/10 rounded-full animate-pulse\"\n            style={{\n              left: `${Math.random() * 100}%`,\n              top: `${Math.random() * 100}%`,\n              animationDelay: `${Math.random() * 3}s`,\n              animationDuration: `${2 + Math.random() * 2}s`\n            }}\n          ></div>\n        ))}\n      </div>\n    </div>\n  );\n}\n\nexport default function Home() {\n  const [isLoading, setIsLoading] = useState(true);\n\n  const handleLoadingComplete = () => {\n    setIsLoading(false);\n  };\n\n  if (isLoading) {\n    return <Loading onLoadingComplete={handleLoadingComplete} loadingDuration={4000} />;\n  }\n\n  return (\n    <div className=\"font-sans grid grid-rows-[20px_1fr_20px] items-center justify-items-center min-h-screen p-8 pb-20 gap-16 sm:p-20 opacity-0 animate-fadeIn\">\n      <main className=\"flex flex-col gap-[32px] row-start-2 items-center sm:items-start\">\n        <Image\n          className=\"dark:invert\"\n          src=\"/next.svg\"\n          alt=\"Next.js logo\"\n          width={180}\n          height={38}\n          priority\n        />\n        <ol className=\"font-mono list-inside list-decimal text-sm/6 text-center sm:text-left\">\n          <li className=\"mb-2 tracking-[-.01em]\">\n            Get started by editing{\" \"}\n            <code className=\"bg-black/[.05] dark:bg-white/[.06] font-mono font-semibold px-1 py-0.5 rounded\">\n              src/app/page.tsx\n            </code>\n            .\n          </li>\n          <li className=\"tracking-[-.01em]\">\n            Save and see your changes instantly.\n          </li>\n        </ol>\n\n        <div className=\"flex gap-4 items-center flex-col sm:flex-row\">\n          <a\n            className=\"rounded-full border border-solid border-transparent transition-colors flex items-center justify-center bg-foreground text-background gap-2 hover:bg-[#383838] dark:hover:bg-[#ccc] font-medium text-sm sm:text-base h-10 sm:h-12 px-4 sm:px-5 sm:w-auto\"\n            href=\"https://vercel.com/new?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app\"\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n          >\n            <Image\n              className=\"dark:invert\"\n              src=\"/vercel.svg\"\n              alt=\"Vercel logomark\"\n              width={20}\n              height={20}\n            />\n            Deploy now\n          </a>\n          <a\n            className=\"rounded-full border border-solid border-black/[.08] dark:border-white/[.145] transition-colors flex items-center justify-center hover:bg-[#f2f2f2] dark:hover:bg-[#1a1a1a] hover:border-transparent font-medium text-sm sm:text-base h-10 sm:h-12 px-4 sm:px-5 w-full sm:w-auto md:w-[158px]\"\n            href=\"https://nextjs.org/docs?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app\"\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n          >\n            Read our docs\n          </a>\n        </div>\n      </main>\n      <footer className=\"row-start-3 flex gap-[24px] flex-wrap items-center justify-center\">\n        <a\n          className=\"flex items-center gap-2 hover:underline hover:underline-offset-4\"\n          href=\"https://nextjs.org/learn?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app\"\n          target=\"_blank\"\n          rel=\"noopener noreferrer\"\n        >\n          <Image\n            aria-hidden\n            src=\"/file.svg\"\n            alt=\"File icon\"\n            width={16}\n            height={16}\n          />\n          Learn\n        </a>\n        <a\n          className=\"flex items-center gap-2 hover:underline hover:underline-offset-4\"\n          href=\"https://vercel.com/templates?framework=next.js&utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app\"\n          target=\"_blank\"\n          rel=\"noopener noreferrer\"\n        >\n          <Image\n            aria-hidden\n            src=\"/window.svg\"\n            alt=\"Window icon\"\n            width={16}\n            height={16}\n          />\n          Examples\n        </a>\n        <a\n          className=\"flex items-center gap-2 hover:underline hover:underline-offset-4\"\n          href=\"https://nextjs.org?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app\"\n          target=\"_blank\"\n          rel=\"noopener noreferrer\"\n        >\n          <Image\n            aria-hidden\n            src=\"/globe.svg\"\n            alt=\"Globe icon\"\n            width={16}\n            height={16}\n          />\n          Go to nextjs.org →\n        </a>\n      </footer>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKA,SAAS,QAAQ,KAA6C;QAA7C,EAAE,iBAAiB,EAAE,kBAAkB,IAAI,EAAE,GAA7C;;IACf,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,MAAM,YAAY,KAAK,GAAG;YAC1B,MAAM,WAAW;8CAAY;oBAC3B,MAAM,UAAU,KAAK,GAAG,KAAK;oBAC7B,MAAM,cAAc,KAAK,GAAG,CAAC,AAAC,UAAU,kBAAmB,KAAK;oBAEhE,YAAY;oBAEZ,mEAAmE;oBACnE;sDAAW;4BACT,mBAAmB;wBACrB;qDAAG;oBAEH,IAAI,eAAe,KAAK;wBACtB,cAAc;wBACd,cAAc;wBAEd,kCAAkC;wBAClC;0DAAW;gCACT,IAAI,mBAAmB;oCACrB;gCACF;4BACF;yDAAG;oBACL;gBACF;6CAAG,KAAK,iBAAiB;YAEzB;qCAAO,IAAM,cAAc;;QAC7B;4BAAG;QAAC;QAAiB;KAAkB;IAEvC,qBACE,6LAAC;QAAI,WAAW,AAAC,6GAAyK,OAA7D,aAAa,wBAAwB;;0BAEhK,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,OAAO,AAAC,GAAW,OAAT,UAAS;4BACnB,WAAW,AAAC,gCAAoD,OAArB,WAAW,MAAM,KAAI;wBAClE;kCAGA,cAAA,6LAAC;4BAAI,WAAU;;;;;;;;;;;kCAIjB,6LAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,SAAS,WAAW,MAAM;wBAAI;;;;;;;;;;;;0BAK3C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACZ,OAAO,KAAK,KAAK,CAAC,kBAAkB,QAAQ,CAAC,GAAG;;;;;;sCAGnD,6LAAC;4BAAK,WAAU;sCACb,OAAO,KAAK,KAAK,CAAC,kBAAkB,QAAQ,CAAC,GAAG;;;;;;;;;;;;;;;;;0BAMvD,6LAAC;gBAAI,WAAU;0BACZ;uBAAI,MAAM;iBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,6LAAC;wBAEC,WAAU;wBACV,OAAO;4BACL,MAAM,AAAC,GAAsB,OAApB,KAAK,MAAM,KAAK,KAAI;4BAC7B,KAAK,AAAC,GAAsB,OAApB,KAAK,MAAM,KAAK,KAAI;4BAC5B,gBAAgB,AAAC,GAAoB,OAAlB,KAAK,MAAM,KAAK,GAAE;4BACrC,mBAAmB,AAAC,GAAwB,OAAtB,IAAI,KAAK,MAAM,KAAK,GAAE;wBAC9C;uBAPK;;;;;;;;;;;;;;;;AAajB;GA3FS;KAAA;AA6FM,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,wBAAwB;QAC5B,aAAa;IACf;IAEA,IAAI,WAAW;QACb,qBAAO,6LAAC;YAAQ,mBAAmB;YAAuB,iBAAiB;;;;;;IAC7E;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAK,WAAU;;kCACd,6LAAC,gIAAA,CAAA,UAAK;wBACJ,WAAU;wBACV,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,QAAQ;;;;;;kCAEV,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC;gCAAG,WAAU;;oCAAyB;oCACd;kDACvB,6LAAC;wCAAK,WAAU;kDAAiF;;;;;;oCAE1F;;;;;;;0CAGT,6LAAC;gCAAG,WAAU;0CAAoB;;;;;;;;;;;;kCAKpC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAU;gCACV,MAAK;gCACL,QAAO;gCACP,KAAI;;kDAEJ,6LAAC,gIAAA,CAAA,UAAK;wCACJ,WAAU;wCACV,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;;;;;;oCACR;;;;;;;0CAGJ,6LAAC;gCACC,WAAU;gCACV,MAAK;gCACL,QAAO;gCACP,KAAI;0CACL;;;;;;;;;;;;;;;;;;0BAKL,6LAAC;gBAAO,WAAU;;kCAChB,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,QAAO;wBACP,KAAI;;0CAEJ,6LAAC,gIAAA,CAAA,UAAK;gCACJ,aAAW;gCACX,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;;;;;;4BACR;;;;;;;kCAGJ,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,QAAO;wBACP,KAAI;;0CAEJ,6LAAC,gIAAA,CAAA,UAAK;gCACJ,aAAW;gCACX,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;;;;;;4BACR;;;;;;;kCAGJ,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,QAAO;wBACP,KAAI;;0CAEJ,6LAAC,gIAAA,CAAA,UAAK;gCACJ,aAAW;gCACX,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;;;;;;4BACR;;;;;;;;;;;;;;;;;;;AAMZ;IA9GwB;MAAA", "debugId": null}}]}