/* [project]/src/app/aeonik_da507f24.module.css [app-client] (css) */
@font-face {
  font-family: aeonik;
  src: url("../media/Aeonik_Medium-s.p.dcab127d.woff2") format("woff2");
  font-display: swap;
}

@font-face {
  font-family: aeonik Fallback;
  src: local(Arial);
  ascent-override: 89.62%;
  descent-override: 20.24%;
  line-gap-override: 0.0%;
  size-adjust: 103.77%;
}

.aeonik_da507f24-module__FjxHoG__className {
  font-family: aeonik, aeonik Fallback;
}

.aeonik_da507f24-module__FjxHoG__variable {
  --font-aeonik: "aeonik", "aeonik Fallback";
}

/*# sourceMappingURL=src_app_aeonik_da507f24_module_css_e59ae46c._.single.css.map*/