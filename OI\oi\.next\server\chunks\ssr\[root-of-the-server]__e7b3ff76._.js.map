{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/Nextjs/Work/OI/oi/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\n\ninterface LoadingProps {\n  onLoadingComplete?: () => void;\n  loadingDuration?: number;\n}\n\nexport default function Loading({ onLoadingComplete, loadingDuration = 4000 }: LoadingProps) {\n  const [progress, setProgress] = useState(0);\n  const [isComplete, setIsComplete] = useState(false);\n\n  useEffect(() => {\n    const startTime = Date.now();\n    const interval = setInterval(() => {\n      const elapsed = Date.now() - startTime;\n      const newProgress = Math.min((elapsed / loadingDuration) * 100, 100);\n      \n      setProgress(newProgress);\n      \n      if (newProgress >= 100) {\n        clearInterval(interval);\n        setIsComplete(true);\n        setTimeout(() => {\n          onLoadingComplete?.();\n        }, 500);\n      }\n    }, 30);\n\n    return () => clearInterval(interval);\n  }, [loadingDuration, onLoadingComplete]);\n\n  return (\n    <div className={`fixed inset-0 bg-black flex flex-col justify-center items-center transition-all duration-500 ${isComplete ? 'opacity-0' : 'opacity-100'}`}>\n      {/* Progress bar container */}\n      <div className=\"w-64 h-2 bg-gray-700 rounded-full overflow-hidden mb-4\">\n        {/* Progress fill */}\n        <div \n          className=\"h-full bg-white transition-all duration-300 ease-out\"\n          style={{ width: `${progress}%` }}\n        />\n      </div>\n      \n      {/* Progress percentage */}\n      <div className=\"text-white text-2xl font-aeonik\">\n        {Math.floor(progress)}%\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASe,SAAS,QAAQ,EAAE,iBAAiB,EAAE,kBAAkB,IAAI,EAAgB;IACzF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,KAAK,GAAG;QAC1B,MAAM,WAAW,YAAY;YAC3B,MAAM,UAAU,KAAK,GAAG,KAAK;YAC7B,MAAM,cAAc,KAAK,GAAG,CAAC,AAAC,UAAU,kBAAmB,KAAK;YAEhE,YAAY;YAEZ,IAAI,eAAe,KAAK;gBACtB,cAAc;gBACd,cAAc;gBACd,WAAW;oBACT;gBACF,GAAG;YACL;QACF,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAiB;KAAkB;IAEvC,qBACE,8OAAC;QAAI,WAAW,CAAC,6FAA6F,EAAE,aAAa,cAAc,eAAe;;0BAExJ,8OAAC;gBAAI,WAAU;0BAEb,cAAA,8OAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,OAAO,GAAG,SAAS,CAAC,CAAC;oBAAC;;;;;;;;;;;0BAKnC,8OAAC;gBAAI,WAAU;;oBACZ,KAAK,KAAK,CAAC;oBAAU;;;;;;;;;;;;;AAI9B", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/Nextjs/Work/OI/oi/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/Nextjs/Work/OI/oi/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/Nextjs/Work/OI/oi/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}]}