'use client';

import { useEffect, useState } from 'react';

interface LoadingProps {
  onLoadingComplete?: () => void;
  loadingDuration?: number;
}

export default function Loading({ onLoadingComplete, loadingDuration = 4000 }: LoadingProps) {
  const [progress, setProgress] = useState(0);
  const [displayProgress, setDisplayProgress] = useState(0);
  const [isComplete, setIsComplete] = useState(false);

  useEffect(() => {
    const startTime = Date.now();
    const interval = setInterval(() => {
      const elapsed = Date.now() - startTime;
      const newProgress = Math.min((elapsed / loadingDuration) * 100, 100);
      
      setProgress(newProgress);
      
      // Animate the display number with slight delay for smoother effect
      setTimeout(() => {
        setDisplayProgress(newProgress);
      }, 50);
      
      if (newProgress >= 100) {
        clearInterval(interval);
        setIsComplete(true);
        
        // Wait a bit before transitioning
        setTimeout(() => {
          if (onLoadingComplete) {
            onLoadingComplete();
          }
        }, 800);
      }
    }, 16); // ~60fps updates

    return () => clearInterval(interval);
  }, [loadingDuration, onLoadingComplete]);

  return (
    <div className={`fixed inset-0 bg-black flex flex-col justify-center items-center transition-all duration-1000 ease-in-out ${isComplete ? 'opacity-0 scale-105' : 'opacity-100 scale-100'}`}>
      {/* Progress Bar Container */}
      <div className="relative w-48 h-2 sm:w-64 sm:h-2 md:w-80 md:h-3 bg-gray-900 rounded-full overflow-hidden shadow-2xl">
        {/* Background glow */}
        <div className="absolute inset-0 bg-gradient-to-r from-gray-800 via-gray-700 to-gray-800 rounded-full"></div>
        
        {/* Progress fill with glow effect */}
        <div 
          className="absolute top-0 left-0 h-full bg-gradient-to-r from-white via-gray-100 to-white rounded-full transition-all duration-100 ease-out shadow-lg"
          style={{ 
            width: `${progress}%`,
            boxShadow: `0 0 20px rgba(255, 255, 255, ${progress / 100 * 0.8}), inset 0 1px 0 rgba(255, 255, 255, 0.5)`
          }}
        >
          {/* Moving shine effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent rounded-full animate-pulse"></div>
        </div>
        
        {/* Outer glow ring */}
        <div 
          className="absolute -inset-1 bg-white/10 rounded-full blur-sm transition-opacity duration-300"
          style={{ opacity: progress / 100 * 0.6 }}
        ></div>
      </div>
      
      {/* Counter with enhanced styling */}
      <div className="absolute bottom-6 left-6 sm:bottom-8 sm:left-8 md:bottom-12 md:left-12">
        <div className="relative">
          {/* Glow background */}
          <div className="absolute inset-0 text-white/20 text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold font-aeonik blur-sm transform translate-x-1 translate-y-1">
            {String(Math.floor(displayProgress)).padStart(3, '0')}
          </div>
          {/* Main text */}
          <span className="relative text-white text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold font-aeonik tracking-wider drop-shadow-2xl">
            {String(Math.floor(displayProgress)).padStart(3, '0')}
          </span>
        </div>
      </div>
      
      {/* Subtle particle effect */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-white/10 rounded-full animate-pulse"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${2 + Math.random() * 2}s`
            }}
          ></div>
        ))}
      </div>
    </div>
  );
}