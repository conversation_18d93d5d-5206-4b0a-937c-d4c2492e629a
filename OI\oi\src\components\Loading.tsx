'use client';

import { useState, useEffect, useRef } from 'react';

interface LoadingProps {
  onLoadingComplete?: () => void;
  loadingDuration?: number;
}

export default function Loading({ onLoadingComplete, loadingDuration = 4000 }: LoadingProps) {
  const [progress, setProgress] = useState(0);
  const [displayNumber, setDisplayNumber] = useState(0);
  const [isComplete, setIsComplete] = useState(false);
  const [isFlipping, setIsFlipping] = useState(false);
  const prevNumberRef = useRef(0);

  useEffect(() => {
    const startTime = Date.now();
    const interval = setInterval(() => {
      const elapsed = Date.now() - startTime;
      const newProgress = Math.min((elapsed / loadingDuration) * 100, 100);
      const newNumber = Math.floor(newProgress);
      
      setProgress(newProgress);
      
      // Only trigger flip when whole number changes
      if (newNumber !== Math.floor(progress)) {
        prevNumberRef.current = displayNumber;
        setIsFlipping(true);
        setTimeout(() => {
          setDisplayNumber(newNumber);
          setIsFlipping(false);
        }, 300); // Match this with flip animation duration
      }
      
      if (newProgress >= 100) {
        clearInterval(interval);
        setIsComplete(true);
        setTimeout(() => {
          onLoadingComplete?.();
        }, 800);
      }
    }, 30);

    return () => clearInterval(interval);
  }, [loadingDuration, onLoadingComplete, progress, displayNumber]);

  return (
    <div className={`fixed inset-0 bg-black flex flex-col justify-center items-center transition-all duration-1000 ease-in-out ${isComplete ? 'opacity-0 scale-105' : 'opacity-100 scale-100'}`}>
      {/* Progress Bar Container */}
      <div className="relative w-48 h-4 sm:w-64 sm:h-4 md:w-80 md:h-5 bg-gray-900 overflow-hidden shadow-2xl">
        <div className="absolute inset-0 bg-gradient-to-r from-gray-800 via-gray-700 to-gray-800"></div>
        
        <div 
          className="absolute top-0 left-0 h-full bg-gradient-to-r from-white via-gray-100 to-white transition-all duration-100 ease-out shadow-lg"
          style={{ 
            width: `${progress}%`,
            boxShadow: `0 0 20px rgba(255, 255, 255, ${progress / 100 * 0.8}), inset 0 1px 0 rgba(255, 255, 255, 0.5)`
          }}
        >
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent rounded-full animate-pulse"></div>
        </div>
        
        <div 
          className="absolute -inset-1 bg-white/10 rounded-full blur-sm transition-opacity duration-300"
          style={{ opacity: progress / 100 * 0.6 }}
        ></div>
      </div>
      
      {/* Flip Clock Counter */}
      <div className="absolute bottom-6 left-6 sm:bottom-8 sm:left-8 md:bottom-12 md:left-12">
        <div className="relative h-16 w-24 perspective-1000">
          {/* Current number */}
          <div className="relative h-full w-full">
            {/* Top half (flipping out) */}
            <div 
              className={`absolute top-0 left-0 right-0 h-1/2 bg-gray-800 flex items-end justify-center overflow-hidden origin-bottom transition-transform duration-300 ease-out ${isFlipping ? 'rotate-x-90' : ''}`}
              style={{
                borderRadius: '8px 8px 0 0',
                backfaceVisibility: 'hidden',
                zIndex: 2,
                transform: isFlipping ? 'rotateX(90deg)' : 'rotateX(0deg)'
              }}
            >
              <span className="text-white text-4xl sm:text-5xl md:text-6xl font-bold tracking-wider drop-shadow-2xl mb-[-0.25em]">
                {String(prevNumberRef.current).padStart(3, '0')}
              </span>
            </div>
            
            {/* Bottom half (static) */}
            <div 
              className="absolute bottom-0 left-0 right-0 h-1/2 bg-gray-800 flex items-start justify-center overflow-hidden"
              style={{
                borderRadius: '0 0 8px 8px',
                zIndex: 1
              }}
            >
              <span className="text-white text-4xl sm:text-5xl md:text-6xl font-bold tracking-wider drop-shadow-2xl mt-[-0.25em]">
                {String(prevNumberRef.current).padStart(3, '0')}
              </span>
            </div>
            
            {/* New top half (flipping in) */}
            <div 
              className={`absolute top-0 left-0 right-0 h-1/2 bg-gray-800 flex items-end justify-center overflow-hidden origin-bottom transition-transform duration-300 ease-out ${isFlipping ? 'rotate-x-0' : 'rotate-x-90'}`}
              style={{
                borderRadius: '8px 8px 0 0',
                backfaceVisibility: 'hidden',
                zIndex: isFlipping ? 3 : 0,
                transform: isFlipping ? 'rotateX(0deg)' : 'rotateX(90deg)'
              }}
            >
              <span className="text-white text-4xl sm:text-5xl md:text-6xl font-bold tracking-wider drop-shadow-2xl mb-[-0.25em]">
                {String(displayNumber).padStart(3, '0')}
              </span>
            </div>
          </div>
          
          {/* Glow background */}
          <div className="absolute inset-0 text-white/20 text-4xl sm:text-5xl md:text-6xl font-bold blur-sm transform translate-x-1 translate-y-1">
            {String(displayNumber).padStart(3, '0')}
          </div>
        </div>
      </div>
      
      {/* Subtle particle effect */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-white/10 rounded-full animate-pulse"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${2 + Math.random() * 2}s`
            }}
          ></div>
        ))}
      </div>
    </div>
  );
}