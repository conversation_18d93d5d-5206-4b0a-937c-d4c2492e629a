{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/Nextjs/Work/OI/oi/src/components/Loading.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useRef } from 'react';\r\n\r\ninterface LoadingProps {\r\n  onLoadingComplete?: () => void;\r\n  loadingDuration?: number;\r\n}\r\n\r\nexport default function Loading({ onLoadingComplete, loadingDuration = 4000 }: LoadingProps) {\r\n  const [progress, setProgress] = useState(0);\r\n  const [displayNumber, setDisplayNumber] = useState(0);\r\n  const [isComplete, setIsComplete] = useState(false);\r\n  const [isFlipping, setIsFlipping] = useState(false);\r\n  const prevNumberRef = useRef(0);\r\n\r\n  useEffect(() => {\r\n    const startTime = Date.now();\r\n    const interval = setInterval(() => {\r\n      const elapsed = Date.now() - startTime;\r\n      const newProgress = Math.min((elapsed / loadingDuration) * 100, 100);\r\n      const newNumber = Math.floor(newProgress);\r\n      \r\n      setProgress(newProgress);\r\n      \r\n      // Only trigger flip when whole number changes\r\n      if (newNumber !== Math.floor(progress)) {\r\n        prevNumberRef.current = displayNumber;\r\n        setIsFlipping(true);\r\n        setTimeout(() => {\r\n          setDisplayNumber(newNumber);\r\n          setIsFlipping(false);\r\n        }, 300); // Match this with flip animation duration\r\n      }\r\n      \r\n      if (newProgress >= 100) {\r\n        clearInterval(interval);\r\n        setIsComplete(true);\r\n        setTimeout(() => {\r\n          onLoadingComplete?.();\r\n        }, 800);\r\n      }\r\n    }, 30);\r\n\r\n    return () => clearInterval(interval);\r\n  }, [loadingDuration, onLoadingComplete, progress, displayNumber]);\r\n\r\n  return (\r\n    <div className={`fixed inset-0 bg-black flex flex-col justify-center items-center transition-all duration-1000 ease-in-out ${isComplete ? 'opacity-0 scale-105' : 'opacity-100 scale-100'}`}>\r\n      {/* Progress Bar Container */}\r\n      <div className=\"relative w-48 h-4 sm:w-64 sm:h-4 md:w-80 md:h-5 bg-gray-900 overflow-hidden shadow-2xl\">\r\n        <div className=\"absolute inset-0 bg-gradient-to-r from-gray-800 via-gray-700 to-gray-800\"></div>\r\n        \r\n        <div \r\n          className=\"absolute top-0 left-0 h-full bg-gradient-to-r from-white via-gray-100 to-white transition-all duration-100 ease-out shadow-lg\"\r\n          style={{ \r\n            width: `${progress}%`,\r\n            boxShadow: `0 0 20px rgba(255, 255, 255, ${progress / 100 * 0.8}), inset 0 1px 0 rgba(255, 255, 255, 0.5)`\r\n          }}\r\n        >\r\n          <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent rounded-full animate-pulse\"></div>\r\n        </div>\r\n        \r\n        <div \r\n          className=\"absolute -inset-1 bg-white/10 rounded-full blur-sm transition-opacity duration-300\"\r\n          style={{ opacity: progress / 100 * 0.6 }}\r\n        ></div>\r\n      </div>\r\n      \r\n      {/* Flip Clock Counter */}\r\n      <div className=\"absolute bottom-6 left-6 sm:bottom-8 sm:left-8 md:bottom-12 md:left-12\">\r\n        <div className=\"relative h-16 w-24 perspective-1000\">\r\n          {/* Current number */}\r\n          <div className=\"relative h-full w-full\">\r\n            {/* Top half (flipping out) */}\r\n            <div \r\n              className={`absolute top-0 left-0 right-0 h-1/2 bg-gray-800 flex items-end justify-center overflow-hidden origin-bottom transition-transform duration-300 ease-out ${isFlipping ? 'rotate-x-90' : ''}`}\r\n              style={{\r\n                borderRadius: '8px 8px 0 0',\r\n                backfaceVisibility: 'hidden',\r\n                zIndex: 2,\r\n                transform: isFlipping ? 'rotateX(90deg)' : 'rotateX(0deg)'\r\n              }}\r\n            >\r\n              <span className=\"text-white text-4xl sm:text-5xl md:text-6xl font-bold tracking-wider drop-shadow-2xl mb-[-0.25em]\">\r\n                {String(prevNumberRef.current).padStart(3, '0')}\r\n              </span>\r\n            </div>\r\n            \r\n            {/* Bottom half (static) */}\r\n            <div \r\n              className=\"absolute bottom-0 left-0 right-0 h-1/2 bg-gray-800 flex items-start justify-center overflow-hidden\"\r\n              style={{\r\n                borderRadius: '0 0 8px 8px',\r\n                zIndex: 1\r\n              }}\r\n            >\r\n              <span className=\"text-white text-4xl sm:text-5xl md:text-6xl font-bold tracking-wider drop-shadow-2xl mt-[-0.25em]\">\r\n                {String(prevNumberRef.current).padStart(3, '0')}\r\n              </span>\r\n            </div>\r\n            \r\n            {/* New top half (flipping in) */}\r\n            <div \r\n              className={`absolute top-0 left-0 right-0 h-1/2 bg-gray-800 flex items-end justify-center overflow-hidden origin-bottom transition-transform duration-300 ease-out ${isFlipping ? 'rotate-x-0' : 'rotate-x-90'}`}\r\n              style={{\r\n                borderRadius: '8px 8px 0 0',\r\n                backfaceVisibility: 'hidden',\r\n                zIndex: isFlipping ? 3 : 0,\r\n                transform: isFlipping ? 'rotateX(0deg)' : 'rotateX(90deg)'\r\n              }}\r\n            >\r\n              <span className=\"text-white text-4xl sm:text-5xl md:text-6xl font-bold tracking-wider drop-shadow-2xl mb-[-0.25em]\">\r\n                {String(displayNumber).padStart(3, '0')}\r\n              </span>\r\n            </div>\r\n          </div>\r\n          \r\n          {/* Glow background */}\r\n          <div className=\"absolute inset-0 text-white/20 text-4xl sm:text-5xl md:text-6xl font-bold blur-sm transform translate-x-1 translate-y-1\">\r\n            {String(displayNumber).padStart(3, '0')}\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      {/* Subtle particle effect */}\r\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\r\n        {[...Array(20)].map((_, i) => (\r\n          <div\r\n            key={i}\r\n            className=\"absolute w-1 h-1 bg-white/10 rounded-full animate-pulse\"\r\n            style={{\r\n              left: `${Math.random() * 100}%`,\r\n              top: `${Math.random() * 100}%`,\r\n              animationDelay: `${Math.random() * 3}s`,\r\n              animationDuration: `${2 + Math.random() * 2}s`\r\n            }}\r\n          ></div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AASe,SAAS,QAAQ,KAA2D;QAA3D,EAAE,iBAAiB,EAAE,kBAAkB,IAAI,EAAgB,GAA3D;;IAC9B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,MAAM,YAAY,KAAK,GAAG;YAC1B,MAAM,WAAW;8CAAY;oBAC3B,MAAM,UAAU,KAAK,GAAG,KAAK;oBAC7B,MAAM,cAAc,KAAK,GAAG,CAAC,AAAC,UAAU,kBAAmB,KAAK;oBAChE,MAAM,YAAY,KAAK,KAAK,CAAC;oBAE7B,YAAY;oBAEZ,8CAA8C;oBAC9C,IAAI,cAAc,KAAK,KAAK,CAAC,WAAW;wBACtC,cAAc,OAAO,GAAG;wBACxB,cAAc;wBACd;0DAAW;gCACT,iBAAiB;gCACjB,cAAc;4BAChB;yDAAG,MAAM,0CAA0C;oBACrD;oBAEA,IAAI,eAAe,KAAK;wBACtB,cAAc;wBACd,cAAc;wBACd;0DAAW;gCACT,8BAAA,wCAAA;4BACF;yDAAG;oBACL;gBACF;6CAAG;YAEH;qCAAO,IAAM,cAAc;;QAC7B;4BAAG;QAAC;QAAiB;QAAmB;QAAU;KAAc;IAEhE,qBACE,6LAAC;QAAI,WAAW,AAAC,6GAAyK,OAA7D,aAAa,wBAAwB;;0BAEhK,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCAEf,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,OAAO,AAAC,GAAW,OAAT,UAAS;4BACnB,WAAW,AAAC,gCAAoD,OAArB,WAAW,MAAM,KAAI;wBAClE;kCAEA,cAAA,6LAAC;4BAAI,WAAU;;;;;;;;;;;kCAGjB,6LAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,SAAS,WAAW,MAAM;wBAAI;;;;;;;;;;;;0BAK3C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCACC,WAAW,AAAC,0JAAyL,OAAhC,aAAa,gBAAgB;oCAClM,OAAO;wCACL,cAAc;wCACd,oBAAoB;wCACpB,QAAQ;wCACR,WAAW,aAAa,mBAAmB;oCAC7C;8CAEA,cAAA,6LAAC;wCAAK,WAAU;kDACb,OAAO,cAAc,OAAO,EAAE,QAAQ,CAAC,GAAG;;;;;;;;;;;8CAK/C,6LAAC;oCACC,WAAU;oCACV,OAAO;wCACL,cAAc;wCACd,QAAQ;oCACV;8CAEA,cAAA,6LAAC;wCAAK,WAAU;kDACb,OAAO,cAAc,OAAO,EAAE,QAAQ,CAAC,GAAG;;;;;;;;;;;8CAK/C,6LAAC;oCACC,WAAW,AAAC,0JAAmM,OAA1C,aAAa,eAAe;oCACjM,OAAO;wCACL,cAAc;wCACd,oBAAoB;wCACpB,QAAQ,aAAa,IAAI;wCACzB,WAAW,aAAa,kBAAkB;oCAC5C;8CAEA,cAAA,6LAAC;wCAAK,WAAU;kDACb,OAAO,eAAe,QAAQ,CAAC,GAAG;;;;;;;;;;;;;;;;;sCAMzC,6LAAC;4BAAI,WAAU;sCACZ,OAAO,eAAe,QAAQ,CAAC,GAAG;;;;;;;;;;;;;;;;;0BAMzC,6LAAC;gBAAI,WAAU;0BACZ;uBAAI,MAAM;iBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,6LAAC;wBAEC,WAAU;wBACV,OAAO;4BACL,MAAM,AAAC,GAAsB,OAApB,KAAK,MAAM,KAAK,KAAI;4BAC7B,KAAK,AAAC,GAAsB,OAApB,KAAK,MAAM,KAAK,KAAI;4BAC5B,gBAAgB,AAAC,GAAoB,OAAlB,KAAK,MAAM,KAAK,GAAE;4BACrC,mBAAmB,AAAC,GAAwB,OAAtB,IAAI,KAAK,MAAM,KAAK,GAAE;wBAC9C;uBAPK;;;;;;;;;;;;;;;;AAajB;GArIwB;KAAA", "debugId": null}}, {"offset": {"line": 248, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/Nextjs/Work/OI/oi/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Image from \"next/image\";\nimport Loading from \"@/components/Loading\";\n\nexport default function Home() {\n  const [isLoading, setIsLoading] = useState(true);\n\n  if (isLoading) {\n    return <Loading onLoadingComplete={() => setIsLoading(false)} />;\n  }\n\n  return (\n    <div className=\"font-sans grid grid-rows-[20px_1fr_20px] items-center justify-items-center min-h-screen p-8 pb-20 gap-16 sm:p-20 animate-fadeIn\">\n      <main className=\"flex flex-col gap-[32px] row-start-2 items-center sm:items-start\">\n        <Image\n          className=\"dark:invert\"\n          src=\"/next.svg\"\n          alt=\"Next.js logo\"\n          width={180}\n          height={38}\n          priority\n        />\n        <ol className=\"font-mono list-inside list-decimal text-sm/6 text-center sm:text-left\">\n          <li className=\"mb-2 tracking-[-.01em]\">\n            Get started by editing{\" \"}\n            <code className=\"bg-black/[.05] dark:bg-white/[.06] font-mono font-semibold px-1 py-0.5 rounded\">\n              src/app/page.tsx\n            </code>\n          </li>\n          <li className=\"tracking-[-.01em]\">\n            Save and see your changes instantly.\n          </li>\n        </ol>\n\n        <div className=\"flex gap-4 items-center flex-col sm:flex-row\">\n          <a\n            className=\"rounded-full border border-solid border-transparent transition-colors flex items-center justify-center bg-foreground text-background gap-2 hover:bg-[#383838] dark:hover:bg-[#ccc] font-medium text-sm sm:text-base h-10 sm:h-12 px-4 sm:px-5 sm:w-auto\"\n            href=\"https://vercel.com/new?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app\"\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n          >\n            <Image\n              className=\"dark:invert\"\n              src=\"/vercel.svg\"\n              alt=\"Vercel logomark\"\n              width={20}\n              height={20}\n            />\n            Deploy now\n          </a>\n          <a\n            className=\"rounded-full border border-solid border-black/[.08] dark:border-white/[.145] transition-colors flex items-center justify-center hover:bg-[#f2f2f2] dark:hover:bg-[#1a1a1a] hover:border-transparent font-medium text-sm sm:text-base h-10 sm:h-12 px-4 sm:px-5 w-full sm:w-auto md:w-[158px]\"\n            href=\"https://nextjs.org/docs?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app\"\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n          >\n            Read our docs\n          </a>\n        </div>\n      </main>\n      <footer className=\"row-start-3 flex gap-[24px] flex-wrap items-center justify-center\">\n        <a\n          className=\"flex items-center gap-2 hover:underline hover:underline-offset-4\"\n          href=\"https://nextjs.org/learn?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app\"\n          target=\"_blank\"\n          rel=\"noopener noreferrer\"\n        >\n          <Image\n            aria-hidden\n            src=\"/file.svg\"\n            alt=\"File icon\"\n            width={16}\n            height={16}\n          />\n          Learn\n        </a>\n        <a\n          className=\"flex items-center gap-2 hover:underline hover:underline-offset-4\"\n          href=\"https://vercel.com/templates?framework=next.js&utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app\"\n          target=\"_blank\"\n          rel=\"noopener noreferrer\"\n        >\n          <Image\n            aria-hidden\n            src=\"/window.svg\"\n            alt=\"Window icon\"\n            width={16}\n            height={16}\n          />\n          Examples\n        </a>\n        <a\n          className=\"flex items-center gap-2 hover:underline hover:underline-offset-4\"\n          href=\"https://nextjs.org?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app\"\n          target=\"_blank\"\n          rel=\"noopener noreferrer\"\n        >\n          <Image\n            aria-hidden\n            src=\"/globe.svg\"\n            alt=\"Globe icon\"\n            width={16}\n            height={16}\n          />\n          Go to nextjs.org →\n        </a>\n      </footer>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,IAAI,WAAW;QACb,qBAAO,6LAAC,gIAAA,CAAA,UAAO;YAAC,mBAAmB,IAAM,aAAa;;;;;;IACxD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAK,WAAU;;kCACd,6LAAC,gIAAA,CAAA,UAAK;wBACJ,WAAU;wBACV,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,QAAQ;;;;;;kCAEV,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC;gCAAG,WAAU;;oCAAyB;oCACd;kDACvB,6LAAC;wCAAK,WAAU;kDAAiF;;;;;;;;;;;;0CAInG,6LAAC;gCAAG,WAAU;0CAAoB;;;;;;;;;;;;kCAKpC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAU;gCACV,MAAK;gCACL,QAAO;gCACP,KAAI;;kDAEJ,6LAAC,gIAAA,CAAA,UAAK;wCACJ,WAAU;wCACV,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;;;;;;oCACR;;;;;;;0CAGJ,6LAAC;gCACC,WAAU;gCACV,MAAK;gCACL,QAAO;gCACP,KAAI;0CACL;;;;;;;;;;;;;;;;;;0BAKL,6LAAC;gBAAO,WAAU;;kCAChB,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,QAAO;wBACP,KAAI;;0CAEJ,6LAAC,gIAAA,CAAA,UAAK;gCACJ,aAAW;gCACX,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;;;;;;4BACR;;;;;;;kCAGJ,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,QAAO;wBACP,KAAI;;0CAEJ,6LAAC,gIAAA,CAAA,UAAK;gCACJ,aAAW;gCACX,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;;;;;;4BACR;;;;;;;kCAGJ,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,QAAO;wBACP,KAAI;;0CAEJ,6LAAC,gIAAA,CAAA,UAAK;gCACJ,aAAW;gCACX,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;;;;;;4BACR;;;;;;;;;;;;;;;;;;;AAMZ;GAzGwB;KAAA", "debugId": null}}]}