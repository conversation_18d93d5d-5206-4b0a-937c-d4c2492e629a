{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/Nextjs/Work/OI/oi/src/components/Loading.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useState } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\n\r\ninterface LoadingProps {\r\n  onLoadingComplete?: () => void;\r\n  loadingDuration?: number;\r\n}\r\n\r\nexport default function Loading({ onLoadingComplete, loadingDuration = 4000 }: LoadingProps) {\r\n  const [progress, setProgress] = useState(0);\r\n  const [isComplete, setIsComplete] = useState(false);\r\n  const router = useRouter();\r\n\r\n  useEffect(() => {\r\n    const startTime = Date.now();\r\n    const interval = setInterval(() => {\r\n      const elapsed = Date.now() - startTime;\r\n      const newProgress = Math.min((elapsed / loadingDuration) * 100, 100);\r\n      \r\n      setProgress(newProgress);\r\n      \r\n      if (newProgress >= 100) {\r\n        clearInterval(interval);\r\n        setIsComplete(true);\r\n        \r\n        // Wait a bit before transitioning\r\n        setTimeout(() => {\r\n          if (onLoadingComplete) {\r\n            onLoadingComplete();\r\n          } else {\r\n            router.push('/home'); // Navigate to main page\r\n          }\r\n        }, 500);\r\n      }\r\n    }, 16); // ~60fps updates\r\n\r\n    return () => clearInterval(interval);\r\n  }, [loadingDuration, onLoadingComplete, router]);\r\n\r\n  return (\r\n    <div className={`fixed inset-0 bg-black flex flex-col justify-center items-center transition-opacity duration-500 ${isComplete ? 'opacity-0' : 'opacity-100'}`}>\r\n      {/* Progress Bar */}\r\n      <div className=\"relative w-32 h-1 bg-gray-800 mb-8\">\r\n        <div \r\n          className=\"absolute top-0 left-0 h-full bg-white transition-all duration-75 ease-out\"\r\n          style={{ width: `${progress}%` }}\r\n        />\r\n      </div>\r\n      \r\n      {/* Counter */}\r\n      <div className=\"absolute bottom-8 left-8\">\r\n        <span className=\"text-white text-6xl font-medium font-aeonik\">\r\n          {String(Math.floor(progress)).padStart(3, '0')}\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAUe,SAAS,QAAQ,KAA2D;QAA3D,EAAE,iBAAiB,EAAE,kBAAkB,IAAI,EAAgB,GAA3D;;IAC9B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,MAAM,YAAY,KAAK,GAAG;YAC1B,MAAM,WAAW;8CAAY;oBAC3B,MAAM,UAAU,KAAK,GAAG,KAAK;oBAC7B,MAAM,cAAc,KAAK,GAAG,CAAC,AAAC,UAAU,kBAAmB,KAAK;oBAEhE,YAAY;oBAEZ,IAAI,eAAe,KAAK;wBACtB,cAAc;wBACd,cAAc;wBAEd,kCAAkC;wBAClC;0DAAW;gCACT,IAAI,mBAAmB;oCACrB;gCACF,OAAO;oCACL,OAAO,IAAI,CAAC,UAAU,wBAAwB;gCAChD;4BACF;yDAAG;oBACL;gBACF;6CAAG,KAAK,iBAAiB;YAEzB;qCAAO,IAAM,cAAc;;QAC7B;4BAAG;QAAC;QAAiB;QAAmB;KAAO;IAE/C,qBACE,6LAAC;QAAI,WAAW,AAAC,oGAA4I,OAAzC,aAAa,cAAc;;0BAE7I,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,OAAO,AAAC,GAAW,OAAT,UAAS;oBAAG;;;;;;;;;;;0BAKnC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAK,WAAU;8BACb,OAAO,KAAK,KAAK,CAAC,WAAW,QAAQ,CAAC,GAAG;;;;;;;;;;;;;;;;;AAKpD;GAjDwB;;QAGP,qIAAA,CAAA,YAAS;;;KAHF", "debugId": null}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/Nextjs/Work/OI/oi/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Image from \"next/image\";\nimport Loading from \"../components/Loading\";\n\nexport default function Home() {\n  const [isLoading, setIsLoading] = useState(true);\n\n  const handleLoadingComplete = () => {\n    setIsLoading(false);\n  };\n\n  if (isLoading) {\n    return <Loading onLoadingComplete={handleLoadingComplete} loadingDuration={4000} />;\n  }\n\n  return (\n    <div className=\"font-sans grid grid-rows-[20px_1fr_20px] items-center justify-items-center min-h-screen p-8 pb-20 gap-16 sm:p-20 opacity-0 animate-fadeIn\">\n      <main className=\"flex flex-col gap-[32px] row-start-2 items-center sm:items-start\">\n        <Image\n          className=\"dark:invert\"\n          src=\"/next.svg\"\n          alt=\"Next.js logo\"\n          width={180}\n          height={38}\n          priority\n        />\n        <ol className=\"font-mono list-inside list-decimal text-sm/6 text-center sm:text-left\">\n          <li className=\"mb-2 tracking-[-.01em]\">\n            Get started by editing{\" \"}\n            <code className=\"bg-black/[.05] dark:bg-white/[.06] font-mono font-semibold px-1 py-0.5 rounded\">\n              src/app/page.tsx\n            </code>\n            .\n          </li>\n          <li className=\"tracking-[-.01em]\">\n            Save and see your changes instantly.\n          </li>\n        </ol>\n\n        <div className=\"flex gap-4 items-center flex-col sm:flex-row\">\n          <a\n            className=\"rounded-full border border-solid border-transparent transition-colors flex items-center justify-center bg-foreground text-background gap-2 hover:bg-[#383838] dark:hover:bg-[#ccc] font-medium text-sm sm:text-base h-10 sm:h-12 px-4 sm:px-5 sm:w-auto\"\n            href=\"https://vercel.com/new?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app\"\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n          >\n            <Image\n              className=\"dark:invert\"\n              src=\"/vercel.svg\"\n              alt=\"Vercel logomark\"\n              width={20}\n              height={20}\n            />\n            Deploy now\n          </a>\n          <a\n            className=\"rounded-full border border-solid border-black/[.08] dark:border-white/[.145] transition-colors flex items-center justify-center hover:bg-[#f2f2f2] dark:hover:bg-[#1a1a1a] hover:border-transparent font-medium text-sm sm:text-base h-10 sm:h-12 px-4 sm:px-5 w-full sm:w-auto md:w-[158px]\"\n            href=\"https://nextjs.org/docs?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app\"\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n          >\n            Read our docs\n          </a>\n        </div>\n      </main>\n      <footer className=\"row-start-3 flex gap-[24px] flex-wrap items-center justify-center\">\n        <a\n          className=\"flex items-center gap-2 hover:underline hover:underline-offset-4\"\n          href=\"https://nextjs.org/learn?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app\"\n          target=\"_blank\"\n          rel=\"noopener noreferrer\"\n        >\n          <Image\n            aria-hidden\n            src=\"/file.svg\"\n            alt=\"File icon\"\n            width={16}\n            height={16}\n          />\n          Learn\n        </a>\n        <a\n          className=\"flex items-center gap-2 hover:underline hover:underline-offset-4\"\n          href=\"https://vercel.com/templates?framework=next.js&utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app\"\n          target=\"_blank\"\n          rel=\"noopener noreferrer\"\n        >\n          <Image\n            aria-hidden\n            src=\"/window.svg\"\n            alt=\"Window icon\"\n            width={16}\n            height={16}\n          />\n          Examples\n        </a>\n        <a\n          className=\"flex items-center gap-2 hover:underline hover:underline-offset-4\"\n          href=\"https://nextjs.org?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app\"\n          target=\"_blank\"\n          rel=\"noopener noreferrer\"\n        >\n          <Image\n            aria-hidden\n            src=\"/globe.svg\"\n            alt=\"Globe icon\"\n            width={16}\n            height={16}\n          />\n          Go to nextjs.org →\n        </a>\n      </footer>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,wBAAwB;QAC5B,aAAa;IACf;IAEA,IAAI,WAAW;QACb,qBAAO,6LAAC,gIAAA,CAAA,UAAO;YAAC,mBAAmB;YAAuB,iBAAiB;;;;;;IAC7E;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAK,WAAU;;kCACd,6LAAC,gIAAA,CAAA,UAAK;wBACJ,WAAU;wBACV,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,QAAQ;;;;;;kCAEV,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC;gCAAG,WAAU;;oCAAyB;oCACd;kDACvB,6LAAC;wCAAK,WAAU;kDAAiF;;;;;;oCAE1F;;;;;;;0CAGT,6LAAC;gCAAG,WAAU;0CAAoB;;;;;;;;;;;;kCAKpC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAU;gCACV,MAAK;gCACL,QAAO;gCACP,KAAI;;kDAEJ,6LAAC,gIAAA,CAAA,UAAK;wCACJ,WAAU;wCACV,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;;;;;;oCACR;;;;;;;0CAGJ,6LAAC;gCACC,WAAU;gCACV,MAAK;gCACL,QAAO;gCACP,KAAI;0CACL;;;;;;;;;;;;;;;;;;0BAKL,6LAAC;gBAAO,WAAU;;kCAChB,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,QAAO;wBACP,KAAI;;0CAEJ,6LAAC,gIAAA,CAAA,UAAK;gCACJ,aAAW;gCACX,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;;;;;;4BACR;;;;;;;kCAGJ,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,QAAO;wBACP,KAAI;;0CAEJ,6LAAC,gIAAA,CAAA,UAAK;gCACJ,aAAW;gCACX,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;;;;;;4BACR;;;;;;;kCAGJ,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,QAAO;wBACP,KAAI;;0CAEJ,6LAAC,gIAAA,CAAA,UAAK;gCACJ,aAAW;gCACX,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;;;;;;4BACR;;;;;;;;;;;;;;;;;;;AAMZ;GA9GwB;KAAA", "debugId": null}}]}