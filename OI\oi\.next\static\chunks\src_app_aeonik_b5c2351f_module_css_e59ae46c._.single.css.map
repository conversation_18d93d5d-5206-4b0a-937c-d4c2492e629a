{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/aeonik_b5c2351f.module.css"], "sourcesContent": ["@font-face {\n    font-family: 'aeonik';\n    src: url('@vercel/turbopack-next/internal/font/local/font?{%22path%22:%22../../public/fonts/Aeonik-Medium.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}') format('woff2');\n    font-display: swap;\n    font-weight: 500;font-style: normal;\n}\n\n@font-face {\n    font-family: 'aeonik Fallback';\n    src: local(\"Arial\");\n    ascent-override: 89.62%;\ndescent-override: 20.24%;\nline-gap-override: 0.00%;\nsize-adjust: 103.77%;\n\n}\n\n.className {\n    font-family: 'aeonik', 'aeonik Fallback';\n    \n}\n.variable {\n    --font-aeonik: 'aeonik', 'aeonik Fallback';\n}\n\n"], "names": [], "mappings": "AAAA;;;;;;;;AAOA;;;;;;;;;AAUA;;;;AAIA"}}]}