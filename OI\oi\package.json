{"name": "oi", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@next/bundle-analyzer": "^15.4.5", "@react-spring/three": "^9.7.5", "@react-spring/web": "^9.7.5", "@react-three/drei": "^10.6.1", "@react-three/fiber": "^9.3.0", "@react-three/gltfjsx": "^4.3.4", "@react-three/postprocessing": "^3.0.4", "@react-three/rapier": "^2.1.0", "@studio-freight/lenis": "^1.0.42", "@types/three": "^0.178.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "draco3d": "^1.5.7", "framer-motion": "^11.18.2", "glsl-noise": "^0.0.0", "gsap": "^3.13.0", "lenis": "^1.3.8", "next": "15.4.5", "next-pwa": "^5.6.0", "postprocessing": "^6.37.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-intersection-observer": "^9.16.0", "react-spring": "^10.0.1", "react-use-measure": "^2.1.7", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "three": "^0.178.0", "valtio": "^2.1.5", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@types/node": "^20.19.9", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4", "typescript": "^5"}}